/* Map container styles */
#map {
  height: 100vh;
  width: 100vw;
}

/* Control panel container */
#container {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: white;
  padding: 15px;
  border: 1px solid #ccc;
  z-index: 1000;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Control panel title */
#container h3 {
  margin: 0 0 10px 0;
  color: #333;
}

/* Statistics section */
#stats {
  margin-bottom: 15px;
}

/* Statistics rows */
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stats-row:last-child {
  margin-bottom: 10px;
}

/* Visited count styling */
.visited-text {
  color: #28a745;
}

.visited-count {
  color: #28a745;
}

/* Not visited count styling */
.not-visited-text {
  color: #6c757d;
}

.not-visited-count {
  color: #6c757d;
}

/* Progress section */
.progress-section {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
}

.progress-bar-container {
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  margin-top: 5px;
}

#progress-bar {
  background: #28a745;
  height: 100%;
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s ease;
}

/* Button container */
.button-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Button styles */
.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn:hover {
  opacity: 0.9;
}

/* File input styling */
#import-file {
  display: none;
}

/* Popup content styling */
.popup-content {
  font-family: Arial, sans-serif;
}

.popup-content h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.checkbox-container {
  margin-bottom: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.checkbox-input {
  transform: scale(1.2);
}

.status-text-visited {
  color: #28a745;
}

.status-text-not-visited {
  color: #6c757d;
}

.popup-info {
  margin: 5px 0;
  color: #666;
}

.popup-info i {
  margin-right: 8px;
  width: 16px;
}
