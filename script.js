// API Configuration
const address = 'https://atlasmedia.mediani.fi/api/v1/public-map-point-markers/';
const format = '/?format=json&page=';
const id = '100';
let page = 1;
const originalUrl = address + id + format + page;
const corsProxyUrl = 'https://corsproxy.io/?';
let url = corsProxyUrl + encodeURIComponent(originalUrl);
const allData = [];
const filteredData = [];

// Function to fetch data from the API
const fetchData = async () => {
  return fetch(url).then(response => response.json());
};

// Function to display data
async function renderData() {
  // Await the promise to get the actual data
  const data = await fetchData();
  
  allData.push(...data.results);
  if (data.next) {
    page++;
    const newUrl = address + id + format + page;
    const newCorsUrl = corsProxyUrl + encodeURIComponent(newUrl);
    url = newCorsUrl;
    renderData();
  } else {
    filterItems();
  }
}

// Function to filter items
function filterItems() {
  filteredData.length = 0; // Clear the array
  filteredData.push(...allData.filter(item => item.name.includes('Jyväskylä')));
  console.log(filteredData);

  // Initialize the map with filtered data after filtering is complete
  initializeMapWithFilteredData();
}

// Initialize the map
var map = L.map('map').setView([62.160871, 25.6416672], 8);
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
  attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
}).addTo(map);

// Data Manager with localStorage for filtered API data
class DataManager {
  constructor() {
    this.originalKey = 'originalFilteredData';
    this.modifiedKey = 'modifiedFilteredData';
    this.statusKey = 'locationStatus';
  }

  // Initialize data with status field
  initializeData(apiData) {
    let originalData = localStorage.getItem(this.originalKey);

    if (!originalData || JSON.parse(originalData).length !== apiData.length) {
      // Add status: false to each item from API data
      const dataWithStatus = apiData.map(place => ({
        id: place.id,
        lat: parseFloat(place.lat),
        lng: parseFloat(place.lng),
        name: place.name,
        status: false
      }));

      localStorage.setItem(this.originalKey, JSON.stringify(dataWithStatus));
      localStorage.setItem(this.modifiedKey, JSON.stringify(dataWithStatus));
      return dataWithStatus;
    } else {
      // Return modified data if exists, otherwise original
      const modified = localStorage.getItem(this.modifiedKey);
      return JSON.parse(modified || originalData);
    }
  }

  // Update status of a location
  updateStatus(locationId, status) {
    const currentData = JSON.parse(localStorage.getItem(this.modifiedKey));
    const locationIndex = currentData.findIndex(place => place.id === locationId);

    if (locationIndex !== -1) {
      currentData[locationIndex].status = status;
      localStorage.setItem(this.modifiedKey, JSON.stringify(currentData));

      // Also store individual status for quick lookup
      const statusData = JSON.parse(localStorage.getItem(this.statusKey) || '{}');
      statusData[locationId] = status;
      localStorage.setItem(this.statusKey, JSON.stringify(statusData));
    }

    return currentData;
  }

  // Get current data
  getCurrentData() {
    const modified = localStorage.getItem(this.modifiedKey);
    const original = localStorage.getItem(this.originalKey);
    return JSON.parse(modified || original || '[]');
  }

  // Get status of a specific location
  getStatus(locationId) {
    const statusData = JSON.parse(localStorage.getItem(this.statusKey) || '{}');
    return statusData[locationId] || false;
  }
}

// Initialize data manager
const dataManager = new DataManager();

// Function to initialize map with filtered data
function initializeMapWithFilteredData() {
  if (filteredData.length > 0) {
    const placesData = dataManager.initializeData(filteredData);
    renderPlaces(placesData);
  }
}

// Handle checkbox change
function handleStatusChange(locationId, checkbox) {
  const newStatus = checkbox.checked;
  dataManager.updateStatus(locationId, newStatus);
  console.log(`Location ${locationId} status changed to: ${newStatus}`);

  // Update the label text and color
  const label = checkbox.parentElement;
  const span = label.querySelector('span');
  span.textContent = newStatus ? 'Visited' : 'Not Visited';
  span.className = newStatus ? 'status-text-visited' : 'status-text-not-visited';

  // Update marker color by finding the marker and changing its icon
  if (markersLayer) {
    markersLayer.eachLayer(function(marker) {
      if (marker.locationId === locationId) {
        const newIcon = newStatus ? greenIcon : greyIcon;
        marker.setIcon(newIcon);
      }
    });
  }

  // Update statistics
  updateStatistics();
}

// Update statistics in the control panel
function updateStatistics() {
  const data = dataManager.getCurrentData();
  const total = data.length;
  const visited = data.filter(place => place.status).length;
  const notVisited = total - visited;
  const progress = total > 0 ? Math.round((visited / total) * 100) : 0;

  document.getElementById('total-count').textContent = total;
  document.getElementById('visited-count').textContent = visited;
  document.getElementById('not-visited-count').textContent = notVisited;
  document.getElementById('progress-percent').textContent = `${progress}%`;
  document.getElementById('progress-bar').style.width = `${progress}%`;
}

// Export data as JSON
function exportData() {
  const data = dataManager.getCurrentData();
  const exportData = {
    exportDate: new Date().toISOString(),
    totalLocations: data.length,
    visitedLocations: data.filter(place => place.status).length,
    data: data
  };

  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = `locations-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// Import data from JSON file
function importData(input) {
  const file = input.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = function(e) {
    try {
      const importedData = JSON.parse(e.target.result);

      // Validate the imported data
      if (importedData.data && Array.isArray(importedData.data)) {
        localStorage.setItem(dataManager.modifiedKey, JSON.stringify(importedData.data));

        // Update status lookup
        const statusData = {};
        importedData.data.forEach(place => {
          if (place.id && typeof place.status === 'boolean') {
            statusData[place.id] = place.status;
          }
        });
        localStorage.setItem(dataManager.statusKey, JSON.stringify(statusData));

        alert('Data imported successfully!');
        location.reload();
      } else {
        alert('Invalid file format. Please select a valid locations JSON file.');
      }
    } catch (error) {
      alert('Error reading file. Please make sure it\'s a valid JSON file.');
    }
  };
  reader.readAsText(file);

  // Clear the input
  input.value = '';
}

// Create custom marker icons
const greyIcon = L.icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-grey.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const greenIcon = L.icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-green.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// Store markers globally for updates
let markersLayer;

// Render data to the map
function renderPlaces(placesData) {
  if (placesData.length === 0) {
    return;
  }

  // Remove existing markers if they exist
  if (markersLayer) {
    map.removeLayer(markersLayer);
  }

  // Create a marker cluster group
  markersLayer = L.markerClusterGroup();

  // Loop through the data and add markers to the cluster group
  placesData.forEach(place => {
    const checkboxId = `status-${place.id}`;

    // Choose icon based on status
    const markerIcon = place.status ? greenIcon : greyIcon;

    const marker = L.marker([place.lat, place.lng], { icon: markerIcon });

    // Create popup content dynamically to always reflect current status
    marker.bindPopup(() => {
      const currentStatus = dataManager.getStatus(place.id);
      const isChecked = currentStatus ? 'checked' : '';

      return `
        <div class="popup-content">
          <h3>${place.name}</h3>
          <div class="checkbox-container">
            <label class="checkbox-label">
              <input
                type="checkbox"
                id="${checkboxId}"
                ${isChecked}
                onchange="handleStatusChange(${place.id}, this)"
                class="checkbox-input"
              />
              <span class="${currentStatus ? 'status-text-visited' : 'status-text-not-visited'}">
                ${currentStatus ? 'Visited' : 'Not Visited'}
              </span>
            </label>
          </div>
          <p class="popup-info">
            <i class="fa-solid fa-map-marker-alt"></i>
            ID: ${place.id}
          </p>
          <p class="popup-info">
            <i class="fa-solid fa-location-dot"></i>
            Coordinates: ${place.lat}, ${place.lng}
          </p>
        </div>
      `;
    });

    // Store location ID in marker for later reference
    marker.locationId = place.id;

    // Add marker to cluster group
    markersLayer.addLayer(marker);
  });

  // Add the cluster group to the map
  map.addLayer(markersLayer);

  // Update statistics after rendering
  updateStatistics();
}

// Initialize the application
function initializeApp() {
  // Uncomment the line below to start fetching data from API
  // renderData();
}
