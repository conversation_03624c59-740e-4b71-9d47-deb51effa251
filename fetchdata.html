<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fetch Data from API Request</title>
</head>
<body>
    <h1>Fetch Data from API Request</h1>
    <script>
        // Construct the API request
        const address = 'https://atlasmedia.mediani.fi/api/v1/public-map-point-markers/'
        const format = '/?format=json&page='
        const id = '100'
        let page = 1
        const originalUrl = address + id + format + page
        const corsProxyUrl = 'https://corsproxy.io/?'
        let url = corsProxyUrl + encodeURIComponent(originalUrl)
        const allData = []
        const filteredData = []
        
        // Function to fetch data from the API
        const fetchData = async () => {
          return fetch(url).then(response => response.json())
        }
        
        // Function to display data
        async function renderData() {
          // Await the promise to get the actual data
          const data = await fetchData()
          
          allData.push(...data.results)
          if (data.next) {
            page++
            const newUrl = address + id + format + page
            const newCorsUrl = corsProxyUrl + encodeURIComponent(newUrl)
            url = newCorsUrl
            renderData()
          } else {
            filterItems()
          }
        }

        // Function to filter items
        function filterItems() {
          const filteredData = allData.filter(item => item.name.includes('Jyväskylä'))
          console.log(filteredData)
        }

        // Call the function to render data
        renderData()
    </script>
</body>
</html>
